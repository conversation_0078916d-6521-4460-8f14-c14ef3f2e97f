import os
import sys
import shutil
import pathlib
import natsort
import numpy as np
import pandas as pd
import tensorflow as tf
import matplotlib.pyplot as plt
import seaborn as sns
import keras
import os
from google.colab import drive
from PIL import Image
from tqdm import tqdm
from tensorflow.keras import regularizers
from tensorflow.keras.preprocessing import image
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense, BatchNormalization,GlobalAveragePooling2D
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping

from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    confusion_matrix,
    classification_report,
)

drive.mount('/content/drive')
!cp "/content/drive/MyDrive/class.zip" "/content/"

# Unzip the dataset
!unzip -q '/content/class.zip' -d '/content/'

# Definisikan direktori dataset utama dan inisialisasi total
datasetDir = '/content/class/'
total_train_images = 0
total_test_images = 0

#Menganalisis Data TRAINING
train_path = os.path.join(datasetDir, 'train')

if not os.path.isdir(train_path):
    print(f"Error: Direktori Training tidak ditemukan di '{train_path}'\n")
else:
    train_class_names = [d for d in os.listdir(train_path) if os.path.isdir(os.path.join(train_path, d))]
    train_class_names.sort()

    if not train_class_names:
        print(f"Tidak ada folder kelas yang ditemukan di dalam '{train_path}'.")
    else:
        print(f"Terdeteksi {len(train_class_names)} kelas folder: {train_class_names}")
        print("\nJumlah Data per Folder Kelas")
        train_class_counts = {}

        for class_name in train_class_names:
            class_path = os.path.join(train_path, class_name)
            num_images_in_class = 0
            sub_folders_found = [] # List untuk menyimpan nama sub-folder

            # Loop ke dalam sub-folder
            for sub_folder_name in os.listdir(class_path):
                sub_folder_path = os.path.join(class_path, sub_folder_name)
                if os.path.isdir(sub_folder_path):
                    # Tambahkan nama sub-folder ke list
                    sub_folders_found.append(sub_folder_name)
                    # Hitung file di dalamnya
                    num_files = len([f for f in os.listdir(sub_folder_path) if os.path.isfile(os.path.join(sub_folder_path, f))])
                    num_images_in_class += num_files

            train_class_counts[class_name] = num_images_in_class
            total_train_images += num_images_in_class
            print(f"Jumlah data dari '{class_name}': {num_images_in_class}")
            # BARIS BARU UNTUK MENAMPILKAN NAMA SUB-FOLDER
            print(f"Folder terdeteksi: {sorted(sub_folders_found)}")

        total_burung_train = train_class_counts.get('hama_burung', 0) + train_class_counts.get('non_hama_burung', 0)
        total_background_train = train_class_counts.get('background', 0)
        print(f"Total data Kelas 'Burung' (hama + non_hama): {total_burung_train}")
        print(f"Total data Kelas 'Background': {total_background_train}")

print(f"Jumlah Total Data di Set Training: {total_train_images}")


# Menganalisis Data TESTING
test_path = os.path.join(datasetDir, 'test')

if not os.path.isdir(test_path):
    print(f"Error: Direktori Testing tidak ditemukan di '{test_path}'\n")
else:
    test_class_names = [d for d in os.listdir(test_path) if os.path.isdir(os.path.join(test_path, d))]
    test_class_names.sort()

    if not test_class_names:
        print(f"Tidak ada folder kelas yang ditemukan di dalam '{test_path}'.")
    else:
        print("\nJumlah Data per Folder Kelas")
        test_class_counts = {}

        for class_name in test_class_names:
            class_path = os.path.join(test_path, class_name)
            num_images_in_class = 0
            sub_folders_found = [] # List untuk menyimpan nama sub-folder

            # Loop ke dalam sub-folder
            for sub_folder_name in os.listdir(class_path):
                sub_folder_path = os.path.join(class_path, sub_folder_name)
                if os.path.isdir(sub_folder_path):
                    # Tambahkan nama sub-folder ke list
                    sub_folders_found.append(sub_folder_name)
                    # Hitung file di dalamnya
                    num_files = len([f for f in os.listdir(sub_folder_path) if os.path.isfile(os.path.join(sub_folder_path, f))])
                    num_images_in_class += num_files

            test_class_counts[class_name] = num_images_in_class
            total_test_images += num_images_in_class
            print(f"Jumlah data dari '{class_name}': {num_images_in_class}")
            # --- BARIS BARU UNTUK MENAMPILKAN NAMA SUB-FOLDER ---
            print(f"Folder terdeteksi: {sorted(sub_folders_found)}")

        total_burung_test = test_class_counts.get('hama_burung', 0) + test_class_counts.get('non_hama_burung', 0)
        total_background_test = test_class_counts.get('background', 0)
        print(f"Total data Kelas 'Burung' (hama + non_hama): {total_burung_test}")
        print(f"Total data Kelas 'Background': {total_background_test}")

print(f"Jumlah Total Data di Set Testing: {total_test_images}")

# Ringkasan Keseluruhan Dataset
print("\nTotal Keseluruhan Dataset")
grand_total = total_train_images + total_test_images
print(f"Total Data Training   : {total_train_images}")
print(f"Total Data Testing    : {total_test_images}")
print(f"Total Gambar Dataset  : {grand_total}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# SAMPLE IMAGES VISUALIZATION
def show_sample_images_per_class(train_dir, class_names, samples_per_class=3):
    """Tampilkan sample gambar dari setiap kelas, termasuk di dalam sub-folder."""

    print("Menampilkan sample gambar per kelas...")

    n_classes = len(class_names)
    fig, axes = plt.subplots(n_classes, samples_per_class, figsize=(samples_per_class * 3, n_classes * 3))

    # Handle case with only one class to avoid indexing issues
    if n_classes == 1:
        axes = np.array([axes]) # Make it a 2D array for consistent indexing
    elif n_classes > 1 and samples_per_class == 1:
         axes = axes.reshape(-1, 1) # Ensure axes is 2D for iteration


    for class_idx, class_name in enumerate(class_names):
        class_path = os.path.join(train_dir, class_name)

        image_files = []
        if os.path.exists(class_path):
            # Recursively find image files in the class directory and its subdirectories
            for root, _, files in os.walk(class_path):
                for file in files:
                    if file.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                        image_files.append(os.path.join(root, file))

            # Shuffle the image files to get random samples each time
            np.random.shuffle(image_files)

            for sample_idx in range(min(samples_per_class, len(image_files))):
                ax = axes[class_idx, sample_idx]

                try:
                    img_path = image_files[sample_idx]
                    img = plt.imread(img_path)
                    ax.imshow(img)

                    if sample_idx == 0:  # Only show class name on first image in the row
                        ax.set_title(f'{class_name}', fontsize=12, fontweight='bold')
                    #else:
                        #ax.set_title(f'Sample {sample_idx + 1}', fontsize=10) # Optional: keep sample numbers

                    ax.axis('off')

                except Exception as e:
                    ax.text(0.5, 0.5, f'Error\nloading\nimage', ha='center', va='center',
                           transform=ax.transAxes, fontsize=10, color='red') # Indicate error visually
                    ax.axis('off')
                    print(f"Error loading image {image_files[sample_idx]} from class {class_name}: {e}")


            # Hide unused subplots for this class if there are fewer images than samples_per_class
            for sample_idx in range(len(image_files), samples_per_class):
                 ax = axes[class_idx, sample_idx]
                 ax.axis('off')
        else:
            print(f"Direktori kelas '{class_name}' tidak ditemukan di {train_dir}. Tidak ada gambar untuk ditampilkan.")
            # Hide all subplots for this class if the directory doesn't exist
            for sample_idx in range(samples_per_class):
                 ax = axes[class_idx, sample_idx]
                 ax.axis('off')


    plt.suptitle('Sample Gambar per Kelas (Dataset Baru)', fontsize=16, fontweight='bold')
    plt.tight_layout(rect=[0, 0.03, 1, 0.95]) # Adjust layout to prevent suptitle overlap
    plt.savefig('sample_images_per_class_new_dataset.png', dpi=300, bbox_inches='tight') # Save with a new filename
    plt.show()

# Jalankan visualisasi sample images dengan path dan kelas baru
# IMPORTANT: Update '/content/binary_dataset/train' if your new dataset path is different
show_sample_images_per_class(
    train_dir='/content/class/train',  # Sesuaikan dengan path dataset baru Anda
    class_names=['hama_burung', 'non_hama_burung', 'background'], # Kelas baru
    samples_per_class=3
)

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

# ===== DATA GENERATOR YANG DIPERBAIKI =====
# Data generator dengan augmentasi yang lebih seimbang untuk mencegah underfitting

train_datagen = ImageDataGenerator(
    rescale=1./255,
    rotation_range=15,      # Tingkatkan sedikit
    width_shift_range=0.1,  # Tingkatkan dari 0.05
    height_shift_range=0.1, # Tingkatkan dari 0.05
    shear_range=0.05,       # Tingkatkan dari 0.02
    zoom_range=0.1,         # Tingkatkan dari 0.05
    horizontal_flip=True,
    vertical_flip=False,
    fill_mode='nearest',
    validation_split=0.3    # Kurangi dari 0.3 untuk lebih banyak data training
)

validation_datagen = ImageDataGenerator(
    rescale=1./255,
    validation_split=0.3 # Tambahkan validasi split
)

# Ukuran batch diperbesar untuk stabilitas training
batch_size = 32
target_size = (224, 224)
data_dir = '/content/dataset/train'

# Generator training - Menunjuk ke subdirektori 'train'
train_generator = train_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='training',
    shuffle=True,
)

# Generator validasi - Menunjuk ke subdirektori 'test'
validation_generator = validation_datagen.flow_from_directory(
    data_dir,
    target_size=target_size,
    batch_size=batch_size,
    class_mode='categorical',
    subset='validation',
    shuffle=False,
)

# Perbarui num_classes berdasarkan generator training
num_classes = train_generator.num_classes

# Rasio data training vs validasi
train_ratio = train_generator.samples / (train_generator.samples + validation_generator.samples)
val_ratio = validation_generator.samples / (train_generator.samples + validation_generator.samples)
print(f"Rasio Data:")
print(f"   • Training: {train_ratio:.1%}")
print(f"   • Validasi: {val_ratio:.1%}")

def create_model():
    model = Sequential()

    model.add(Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 3)))
    model.add(MaxPooling2D((2, 2)))

    model.add(Conv2D(64, (3, 3), activation='relu'))
    model.add(MaxPooling2D((2, 2)))

    model.add(Conv2D(128, (3, 3), activation='relu'))
    model.add(MaxPooling2D((2, 2)))

    model.add(Conv2D(128, (3, 3), activation='relu'))
    model.add(MaxPooling2D((2, 2)))

    model.add(Flatten())
    model.add(Dense(64, activation='relu'))
    model.add(Dropout(0.5))
    model.add(Dense(5, activation='sigmoid'))

    # Learning rate tinggi
    model.compile(loss='categorical_crossentropy',optimizer='adam',metrics=['accuracy'])

    model.summary()
    return model

# Ganti model lama dengan yang diperbaiki
model = create_model()

#PERSIAPAN GAMBAR UNTUK VISUALISASI
import random

# Ambil satu path gambar acak dari direktori test
try:
    # Direktori data test
    test_data_dir = '/content/dataset/test'
    # Ambil nama kelas secara acak
    random_class = random.choice(os.listdir(test_data_dir))
    class_path = os.path.join(test_data_dir, random_class)
    # Ambil nama file gambar secara acak dari kelas tersebut
    random_image_file = random.choice(os.listdir(class_path))
    image_path = os.path.join(class_path, random_image_file)

    print(f"Gambar yang dipilih untuk visualisasi: {image_path}")

    # Load dan proses gambar
    target_size = (224, 224) # Pastikan target_size sesuai dengan yang digunakan saat training
    img = image.load_img(image_path, target_size=target_size)
    img_array = image.img_to_array(img)
    img_array_expanded = np.expand_dims(img_array, axis=0) # Shape jadi (1, 224, 224, 3)

    # Tampilkan gambar sampel
    plt.imshow(img)
    plt.title(f"Gambar Sampel: {random_class}")
    plt.axis('off')
    plt.show()

except Exception as e:
    print(f"Gagal memuat gambar sampel. Error: {e}")
    print("Pastikan path direktori test sudah benar dan berisi gambar.")

#MEMBUAT MODEL UNTUK EKSTRAKSI FITUR

# Ambil indeks dari setiap jenis lapisan yang ingin divisualisasikan
# Sesuaikan indeks ini jika Anda mengubah arsitektur model Anda
conv_layer_indices = [i for i, layer in enumerate(model.layers) if 'conv2d' in layer.name]
maxpool_layer_indices = [i for i, layer in enumerate(model.layers) if 'max_pooling2d' in layer.name]

# Ambil output dari setiap lapisan tersebut
conv_outputs = [model.layers[i].output for i in conv_layer_indices]
maxpool_outputs = [model.layers[i].output for i in maxpool_layer_indices]

# Buat model baru yang inputnya sama dengan model asli, tetapi outputnya adalah peta fitur
feature_map_conv_model = tf.keras.Model(inputs=model.inputs, outputs=conv_outputs)
feature_map_maxpool_model = tf.keras.Model(inputs=model.inputs, outputs=maxpool_outputs)

print("Model untuk ekstraksi fitur berhasil dibuat.")

#MENJALANKAN PREDIKSI & MENDAPATKAN PETA FITUR

# Pastikan gambar sampel (img_array_expanded) sudah ada dari Blok 1
try:
    # Jalankan prediksi pada gambar untuk mendapatkan peta fitur
    conv_feature_maps = feature_map_conv_model.predict(img_array_expanded)
    maxpool_feature_maps = feature_map_maxpool_model.predict(img_array_expanded)

    print("Peta fitur berhasil diekstrak.")
    # Contoh: Tampilkan bentuk output dari lapisan konvolusi pertama
    print(f"Bentuk output Conv Layer pertama: {conv_feature_maps[0].shape}")
    print(f"Bentuk output MaxPool Layer pertama: {maxpool_feature_maps[0].shape}")
except NameError:
    print("Variabel 'img_array_expanded' tidak ditemukan. Pastikan Blok 1 sudah dijalankan.")
except Exception as e:
    print(f"Terjadi error saat ekstraksi fitur: {e}")

#FUNGSI UNTUK MENAMPILKAN PETA FITUR

def show_feature_maps(layer_names, feature_maps, features_per_row=8, max_features_to_show=None):
    """
    Menampilkan visualisasi peta fitur (feature maps) dari lapisan-lapisan CNN.
    Membatasi jumlah peta fitur yang ditampilkan per lapisan.
    """
    for layer_name, feature_map in zip(layer_names, feature_maps):
        # feature_map memiliki shape (1, tinggi, lebar, jumlah_filter)
        num_features = feature_map.shape[-1]

        # Tentukan berapa banyak fitur yang akan ditampilkan
        display_features = num_features
        if max_features_to_show is not None and max_features_to_show < num_features:
            display_features = max_features_to_show

        if display_features == 0:
            print(f"Tidak ada peta fitur untuk ditampilkan untuk lapisan: {layer_name}")
            continue

        # Atur ukuran grid untuk subplot
        n_cols = features_per_row
        n_rows = (display_features + n_cols - 1) // n_cols # Menghitung jumlah baris yang dibutuhkan

        plt.figure(figsize=(n_cols * 1.5, n_rows * 1.5))
        plt.suptitle(f"Peta Fitur dari Layer: {layer_name} (Shape: {feature_map.shape})", fontsize=16)

        for i in range(display_features):
            ax = plt.subplot(n_rows, n_cols, i + 1)
            # Tampilkan setiap channel dari peta fitur
            plt.imshow(feature_map[0, :, :, i], cmap='viridis')
            ax.set_xticks([])
            ax.set_yticks([])

        plt.tight_layout()
        plt.show()

print("Fungsi 'show_feature_maps' berhasil didefinisikan.")

#VISUALISASI HASIL
# Pastikan semua variabel dari blok sebelumnya sudah ada
try:
    # Ambil nama lapisan untuk judul plot
    conv_layer_names = [model.layers[i].name for i in conv_layer_indices]
    maxpool_layer_names = [model.layers[i].name for i in maxpool_layer_indices]

    # Tentukan jumlah maksimum peta fitur yang ingin ditampilkan per lapisan
    max_features = 8 # Ubah angka ini sesuai keinginan Anda

    print("\nVisualisasi Peta Fitur dari Lapisan Konvolusi (Conv2D):")
    show_feature_maps(conv_layer_names, conv_feature_maps, features_per_row=8, max_features_to_show=max_features)

    print("\nVisualisasi Peta Fitur dari Lapisan Pooling (MaxPooling2D):")
    show_feature_maps(maxpool_layer_names, maxpool_feature_maps, features_per_row=8, max_features_to_show=max_features)

except NameError:
    print("Variabel tidak ditemukan. Pastikan Blok 1, 2, dan 3 sudah dijalankan.")
except Exception as e:
    print(f"Terjadi error saat visualisasi: {e}")

## ===== BLOK CALLBACK YANG DISEMPURNAKAN =====
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau

# Buat direktori untuk menyimpan model jika belum ada
model_save_dir = '/content/saved_models/'
if not os.path.exists(model_save_dir):
    os.makedirs(model_save_dir)

# Path lengkap untuk file model terbaik
best_model_path = os.path.join(model_save_dir, 'best_bird_classification_model.h5')

checkpoint = ModelCheckpoint(
    filepath=best_model_path,
    monitor='val_loss',      # Memantau validation loss
    mode='min',              # Mode 'min' karena kita ingin loss sekecil mungkin
    save_best_only=True,     # Hanya menyimpan model jika 'val_loss' membaik
    verbose=1                # Memberi notifikasi saat model disimpan
)

# Mencegah overfitting dan menghemat waktu.
early_stop = EarlyStopping(
    monitor='val_accuracy',    # Konsisten dengan ReduceLROnPlateau
    mode='max',               # Karena kita ingin accuracy maksimal
    patience=15,              # Lebih toleran untuk dataset kompleks
    min_delta=0.001,          # Minimum improvement yang dianggap signifikan
    verbose=1,
    restore_best_weights=True
)

# Inisialisasi ReduceLROnPlateau dan tetapkan ke variabel
reduce_lr = ReduceLROnPlateau(
    monitor='val_accuracy',   # Konsisten dengan EarlyStopping
    mode='max',              # Maksimalkan accuracy
    factor=0.5,
    patience=7,              # Lebih kecil dari EarlyStopping patience
    min_lr=0.00001,
    min_delta=0.001,         # Tambahkan min_delta
    verbose=1
)

class UnderfittingMonitor(tf.keras.callbacks.Callback):
    def on_epoch_end(self, epoch, logs=None):
        train_acc = logs.get('accuracy', 0)
        val_acc = logs.get('val_accuracy', 0)

        if epoch > 10 and train_acc < 0.7:  # Jika training accuracy rendah
            print(f"\n Possible underfitting detected at epoch {epoch+1}")
            print(f"Training accuracy: {train_acc:.4f}")

        if train_acc < val_acc:  # Validation lebih tinggi dari training
            print(f"\nModel might benefit from less regularization")

underfitting_monitor = UnderfittingMonitor()

callbacks = [checkpoint, early_stop, reduce_lr, underfitting_monitor]

print(f"ModelCheckpoint akan menyimpan model terbaik di: {best_model_path}")

# ===== TRAINING MODEL =====
# Hitung langkah per epoch
steps_per_epoch = max(1, train_generator.samples // batch_size)
validation_steps = max(1, validation_generator.samples // batch_size)

# Latih model
history = model.fit(
    train_generator,
    steps_per_epoch=steps_per_epoch,
    epochs=30,
    validation_data=validation_generator,
    validation_steps=validation_steps,
    callbacks=callbacks
)

# ===== VISUALISASI RIWAYAT TRAINING =====
print("📊 Memvisualisasikan riwayat training...")

# Ekstrak riwayat training
acc = history.history['accuracy']
val_acc = history.history['val_accuracy']
loss = history.history['loss']
val_loss = history.history['val_loss']
epochs_range = range(len(acc))

# Buat subplot
plt.figure(figsize=(15, 5))

# Plot akurasi
plt.subplot(1, 3, 1)
plt.plot(epochs_range, acc, label='Akurasi Training', linewidth=2)
plt.plot(epochs_range, val_acc, label='Akurasi Validasi', linewidth=2)
plt.title('Akurasi Training dan Validasi', fontsize=14, fontweight='bold')
plt.xlabel('Epochs')
plt.ylabel('Akurasi')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot loss
plt.subplot(1, 3, 2)
plt.plot(epochs_range, loss, label='Loss Training', linewidth=2)
plt.plot(epochs_range, val_loss, label='Loss Validasi', linewidth=2)
plt.title('Loss Training dan Validasi', fontsize=14, fontweight='bold')
plt.xlabel('Epochs')
plt.ylabel('Loss')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot laju pembelajaran (jika tersedia)
plt.subplot(1, 3, 3)
if 'lr' in history.history:
    plt.plot(epochs_range, history.history['lr'], label='Laju Pembelajaran', linewidth=2, color='orange')
    plt.title('Jadwal Laju Pembelajaran', fontsize=14, fontweight='bold')
    plt.xlabel('Epochs')
    plt.ylabel('Laju Pembelajaran')
    plt.yscale('log')
    plt.legend()
    plt.grid(True, alpha=0.3)
else:
    # Tampilkan metrik akhir sebagai gantinya
    final_acc = acc[-1]
    final_val_acc = val_acc[-1]
    final_loss = loss[-1]
    final_val_loss = val_loss[-1]

    metrics_text = f"Metrik Akhir:\n\n"
    metrics_text += f"Akurasi Training: {final_acc:.4f}\n"
    metrics_text += f"Akurasi Validasi: {final_val_acc:.4f}\n\n"
    metrics_text += f"Loss Training: {final_loss:.4f}\n"
    metrics_text += f"Loss Validasi: {final_val_loss:.4f}"

    plt.text(0.1, 0.5, metrics_text, fontsize=12,
             bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.7),
             transform=plt.gca().transAxes)
    plt.title('Metrik Training Akhir', fontsize=14, fontweight='bold')
    plt.axis('off')

plt.tight_layout()
plt.show()

# Simpan riwayat training
results_dir = './results'
os.makedirs(results_dir, exist_ok=True)

# Simpan sebagai CSV
history_df = pd.DataFrame(history.history)
history_df.to_csv(os.path.join(results_dir, 'training_history.csv'), index=False)

print(f"Riwayat training disimpan di: {os.path.join(results_dir, 'training_history.csv')}")

# ===== CROSS-VALIDATION K-FOLD =====
print("⏳ Memulai Cross-Validation K-Fold...")

from sklearn.model_selection import KFold
import numpy as np

# Definisikan jumlah lipatan (folds)
n_splits = 5 # Anda bisa menyesuaikan angka ini

# Siapkan data untuk K-Fold
# Perlu mendapatkan path file dan label dari direktori training
train_data_dir = os.path.join(datasetDir, 'train') # Menggunakan datasetDir sebagaimana diklarifikasi
if not os.path.exists(train_data_dir):
    print(f"Error: Direktori data training tidak ditemukan di {train_data_dir}. Tidak dapat melakukan cross-validation.")
else:
    # Dapatkan semua path gambar dan label yang sesuai dari direktori training
    all_image_paths = []
    all_image_labels = []
    print(f"Mengumpulkan path gambar dan label dari: {train_data_dir}")

    # Pastikan bird_classes didefinisikan dan berisi nama-nama kelas
    if 'bird_classes' not in locals() or not bird_classes:
        print("Error: 'bird_classes' tidak didefinisikan. Harap jalankan sel konfigurasi data terlebih dahulu.")
    else:
        class_names_list = bird_classes # Gunakan daftar nama kelas

        for class_name in class_names_list:
            class_path = os.path.join(train_data_dir, class_name)
            if os.path.exists(class_path):
                image_files = [os.path.join(class_path, f) for f in os.listdir(class_path)
                               if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]
                all_image_paths.extend(image_files)
                all_image_labels.extend([class_name] * len(image_files)) # Tetapkan label untuk setiap gambar

        if not all_image_paths:
             print("Tidak ada gambar ditemukan di direktori training.")
        else:
            # Petakan label string ke indeks integer
            label_to_index = {name: i for i, name in enumerate(class_names_list)}
            all_image_indices = np.array([label_to_index[label] for label in all_image_labels])
            all_image_paths = np.array(all_image_paths)

            print(f"Ditemukan {len(all_image_paths)} total gambar training.")

            # Inisialisasi KFold
            kf = KFold(n_splits=n_splits, shuffle=True, random_state=42) # Menambahkan random_state untuk reproduksibilitas

            # Simpan hasil dari setiap fold
            fold_accuracies = []
            fold_losses = []

            print(f"\nMenjalankan Cross-Validation {n_splits}-Fold...")

            # Loop melalui setiap fold
            for fold, (train_index, val_index) in enumerate(kf.split(all_image_paths, all_image_indices)):
                print(f"\n--- Fold {fold+1}/{n_splits} ---")

                # Dapatkan data train dan validasi untuk fold ini
                X_train_fold, X_val_fold = all_image_paths[train_index], all_image_paths[val_index]
                y_train_fold_indices, y_val_fold_indices = all_image_indices[train_index], all_image_indices[val_index]

                # Ubah label integer menjadi one-hot encoded (diperlukan untuk categorical_crossentropy)
                y_train_fold_one_hot = tf.keras.utils.to_categorical(y_train_fold_indices, num_classes=num_classes)
                y_val_fold_one_hot = tf.keras.utils.to_categorical(y_val_fold_indices, num_classes=num_classes)


                # Buat Data Generator untuk fold ini
                # Catatan: Kita perlu membuat generator baru untuk setiap fold untuk menggunakan subset data yang benar
                fold_train_datagen = ImageDataGenerator(
                    rescale=1./255,
                    rotation_range=15,
                    width_shift_range=0.1,
                    height_shift_range=0.1,
                    shear_range=0.1,
                    zoom_range=0.15,
                    horizontal_flip=True,
                    vertical_flip=False,
                    fill_mode='nearest'
                )

                fold_val_datagen = ImageDataGenerator(rescale=1./255)

                # Gunakan flow_from_dataframe atau yang serupa jika pathing langsung kompleks dengan indeks KFold
                # Pendekatan sederhana adalah memuat gambar secara manual untuk dataset yang lebih kecil atau menggunakan tf.data pipeline untuk yang besar
                # Mengingat struktur saat ini, mari kita coba pendekatan sederhana dengan meneruskan array secara langsung
                # Ini memerlukan pemuatan gambar ke dalam memori, yang mungkin menjadi masalah untuk dataset yang sangat besar.
                # Untuk dataset besar, tf.data pipeline akan lebih cocok, tetapi memerlukan perubahan kode yang signifikan.
                # Untuk saat ini, mari kita asumsikan ukuran dataset memungkinkan pemuatan subset ke dalam memori untuk setiap fold.

                print("Memuat gambar untuk fold saat ini...")
                X_train_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_train_fold])
                X_val_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_val_fold])

                # Rescale gambar
                X_train_images_fold /= 255.0
                X_val_images_fold /= 255.0

                # Buat ulang arsitektur model untuk setiap fold untuk memastikan awal yang baru
                # (Bobot tidak dibagikan antar fold)
                # Anda mungkin ingin menyimpan arsitektur model secara terpisah dan memuatnya di sini
                # Untuk saat ini, mari kita asumsikan definisi model tersedia secara global atau dapat dibuat ulang.
                # (Ini mengasumsikan sel kode arsitektur model sudah dijalankan)

                # Buat ulang model menggunakan definisi Sequential dari sel REo-2V8bBKGL
                # Ini adalah placeholder - idealnya, definisikan fungsi untuk membuat model
                print("Membuat ulang model untuk fold saat ini...")
                try:
                    fold_model = Sequential([
                        # Blok 1
                        Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(*target_size, 3)),
                        Conv2D(32, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Blok 2
                        Conv2D(64, (3, 3), activation='relu', padding='same'),
                        Conv2D(64, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Blok 3
                        Conv2D(128, (3, 3), activation='relu', padding='same'),
                        Conv2D(128, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Blok 4
                        Conv2D(256, (3, 3), activation='relu', padding='same'),
                        Conv2D(256, (3, 3), activation='relu', padding='same'),
                        MaxPooling2D((2, 2)),
                        Dropout(0.25),

                        # Classifier
                        Flatten(),
                        Dense(512, activation='relu'),
                        Dropout(0.5),
                        Dense(256, activation='relu'),
                        Dropout(0.5),
                        Dense(num_classes, activation='softmax') # Gunakan num_classes yang didefinisikan sebelumnya
                    ])

                    # Kompilasi model
                    fold_model.compile(
                        optimizer=Adam(learning_rate=0.001),
                        loss='categorical_crossentropy',
                        metrics=['accuracy']
                    )
                    print("Model berhasil dibuat ulang dan dikompilasi.")

                except Exception as e:
                    print(f"Error saat membuat ulang model untuk fold {fold+1}: {e}")
                    continue # Lewati fold ini jika pembuatan ulang model gagal


                # Latih model pada data training fold
                print("Melatih model untuk fold saat ini...")
                # Gunakan fit() dengan array data
                history_fold = fold_model.fit(
                    fold_train_datagen.flow(X_train_images_fold, y_train_fold_one_hot, batch_size=batch_size),
                    epochs=10, # Epoch dikurangi untuk CV lebih cepat, sesuaikan sesuai kebutuhan
                    validation_data=fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size),
                    verbose=0 # Atur ke 1 untuk melihat progres training per epoch
                )
                print(f"Training selesai untuk fold {fold+1}.")

                # Evaluasi model pada data validasi fold
                print(f"Mengevaluasi model untuk fold {fold+1}...")
                loss_fold, accuracy_fold = fold_model.evaluate(fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size), verbose=0)
                print(f"Fold {fold+1} - Loss: {loss_fold:.4f}, Akurasi: {accuracy_fold:.4f}")

                fold_losses.append(loss_fold)
                fold_accuracies.append(accuracy_fold)

                # Opsional: Bersihkan sesi untuk membebaskan memori, terutama untuk banyak fold
                tf.keras.backend.clear_session()


            # Laporkan hasil rata-rata di semua fold
            print("\n--- Ringkasan Cross-Validation ---")
            print(f"Akurasi Validasi Rata-rata: {np.mean(fold_accuracies):.4f} (+/- {np.std(fold_accuracies):.4f})")
            print(f"Loss Validasi Rata-rata: {np.mean(fold_losses):.4f} (+/- {np.std(fold_losses):.4f})")
            print("✅ Cross-Validation K-Fold selesai.")


# To delete a directory and its contents:
!rm -r /content/dataset

# Be careful when using rm, as deleted files cannot be recovered.
# Uncomment the line you want to use and replace the path with the actual path to your file or directory.