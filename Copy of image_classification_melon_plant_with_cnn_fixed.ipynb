!pip install tensorflow

import os
import sys
import shutil
from zipfile import ZipFile as zf
import pathlib
import natsort

import numpy as np
import pandas as pd
import tensorflow as tf
import matplotlib.pyplot as plt
import seaborn as sns

from PIL import Image
from re import search
from tqdm import tqdm
from google.colab import drive
from tensorflow.keras.preprocessing import image
from tensorflow.keras.preprocessing.image import ImageDataGenerator
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense

from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    confusion_matrix,
    classification_report
)

drive.mount('/content/drive')
!cp /content/drive/MyDrive/datasets.zip  /home/<USER>

drive.mount('/content/drive')
!cp /content/drive/MyDrive/datasets.zip  /home/<USER>

datasetDir = '/home/<USER>/'
# show amount of data for each diseases type
print("amount data of healthy : ", len(os.listdir(datasetDir + 'images/train/healthy')))
print("amount data of leaf spot : ", len(os.listdir(datasetDir + 'images/train/kutu-daun')))
print("amount data of layu fusarium : ", len(os.listdir(datasetDir + 'images/train/layu-fusarium')))
print("amount data of ulat pengorok daun : ", len(os.listdir(datasetDir + 'images/train/ulat-daun')))
print("amount data of unkown data : ", len(os.listdir(datasetDir + 'images/train/unknown')))
print("ammount data of testing directory : ", len(os.listdir(datasetDir + 'images/test')))

# Buat direktori preview jika belum ada
os.makedirs(datasetDir + 'images/preview', exist_ok=True)

# Ambil path dari gambar terakhir pada folder healthy
path_aug = os.path.join(datasetDir + 'images/train/healthy', os.listdir(datasetDir + 'images/train/healthy')[-1])

# Load dan konversi gambar ke array
img_augmentation = image.load_img(path_aug)
x_aug = image.img_to_array(img_augmentation)
x_aug = x_aug.reshape((1,) + x_aug.shape)

# Augmentasi gambar
datagen = ImageDataGenerator(
    rescale=1./255,
    shear_range=0.2,
    zoom_range=0.2,
    horizontal_flip=True,
    vertical_flip=True,
    validation_split=0.2  # 20% for validation
)

i = 0
for batch in datagen.flow(x_aug, batch_size=1, save_to_dir=datasetDir + 'images/preview', save_prefix='disease', save_format='jpeg'):
    i += 1
    if i >= 20:
        break

# Tampilkan hasil preview
preview_img = os.listdir(datasetDir + 'images/preview')

plt.figure(figsize=(15, 15))
for n in range(len(preview_img)):
    plt.subplot((len(preview_img) // 4) + 1, 4, n + 1)
    plt.subplots_adjust(hspace=0.3)
    plt.imshow(image.load_img(os.path.join(datasetDir + 'images/preview', preview_img[n]),
                              color_mode="rgb",
                              target_size=(224, 224),
                              interpolation="nearest"))
    plt.axis('off')
plt.show()


train_datagen = datagen.flow_from_directory(
    r'/home/<USER>/images/train',
    target_size=(224, 224),
    batch_size=8,
    class_mode='categorical',
    subset='training'  # This uses the training portion
)

val_datagen = datagen.flow_from_directory(
    r'/home/<USER>/images/train',  # Same directory!
    target_size=(224, 224),
    batch_size=8,
    class_mode='categorical',
    subset='validation'  # This uses the validation portion
)

model = tf.keras.models.Sequential([
    tf.keras.layers.Conv2D(64,(3,3),activation='relu',padding='same', input_shape=(224, 224, 3)),
    tf.keras.layers.MaxPooling2D(2,2),
    tf.keras.layers.Dropout(0.2),
    tf.keras.layers.Conv2D(128,(3,3),activation='relu',padding='same'),
    tf.keras.layers.MaxPooling2D(2,2),
    tf.keras.layers.Dropout(0.2),
    tf.keras.layers.Conv2D(256,(3,3),activation='relu',padding='same'),
    tf.keras.layers.MaxPooling2D(2,2),
    tf.keras.layers.Dropout(0.2),
    tf.keras.layers.Conv2D(512,(3,3),activation='relu',padding='same'),
    tf.keras.layers.MaxPooling2D(2,2),
    tf.keras.layers.Dropout(0.2),
    tf.keras.layers.Flatten(),
    tf.keras.layers.Dense(5, activation='softmax')
])

model.compile(optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy'])

model.summary()

tf.keras.utils.plot_model(model, to_file="/home/<USER>")

# for layer_name, feature_map in zip(layer_names, feature_maps):
#   if len(feature_map.shape) == 4:
#     n_features  = feature_map.shape[-1]
#     size=feature_map.shape[1]
#     display_grid = np.zeros((size, size * n_features))
#     for i in range(n_features):
#       feature_image = feature_map[0, :, :, i]
#       feature_image-= feature_image.mean()
#       feature_image/= feature_image.std ()
#       feature_image*=  64
#       feature_image+= 128
#       feature_image = np.clip(feature_image, 0, 255).astype('uint8')
#       display_grid[:, i * size : (i + 1) * size] = feature_image
#     scale = 20. / n_features
#     plt.figure( figsize=(scale * n_features, scale))
#     plt.title(layer_name)
#     plt.grid(False)
#     plt.imshow(display_grid, cmap='gray')
#     plt.show()

# 1. Definisikan model Sequential
model = Sequential([
    Conv2D(32, (3, 3), activation='relu', input_shape=(224, 224, 3)),  # index 0
    MaxPooling2D((2, 2)),                                              # index 1
    Dropout(0.2),                                                      # index 2
    Conv2D(64, (3, 3), activation='relu'),                             # index 3
    MaxPooling2D((2, 2)),                                              # index 4
    Dropout(0.2),                                                      # index 5
    Conv2D(128, (3, 3), activation='relu'),                            # index 6
    MaxPooling2D((2, 2)),                                              # index 7
    Dropout(0.2),                                                      # index 8
    Conv2D(128, (3, 3), activation='relu'),                            # index 9
    MaxPooling2D((2, 2)),                                              # index 10
    Dropout(0.2),                                                      # index 11
    Flatten(),
    Dense(64, activation='relu'),
    Dense(5, activation='sigmoid')
])

model.compile(
    optimizer=tf.keras.optimizers.Adam(learning_rate=0.001),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)

model.summary()

# 2. Ambil indeks tiap jenis layer
convLayerIdx = [0, 3, 6, 9]
maxPoolingLayerIdx = [1, 4, 7, 10]
dropoutLayerIdx = [2, 5, 8, 11]

# 3. Ambil output tiap layer
convLayersOutput = [model.layers[i].output for i in convLayerIdx]
maxPoolingLayersOutput = [model.layers[i].output for i in maxPoolingLayerIdx]
dropoutLayersOutput = [model.layers[i].output for i in dropoutLayerIdx]

# 4. Buat model untuk mengambil output feature map dari setiap layer
featureMapConvLayerModel = tf.keras.Model(inputs=model.inputs, outputs=convLayersOutput)
featureMapMaxPoolingLayerModel = tf.keras.Model(inputs=model.inputs, outputs=maxPoolingLayersOutput)
featureMapDropoutLayerModel = tf.keras.Model(inputs=model.inputs, outputs=dropoutLayersOutput)

# 5. Load dan proses gambar
image_path = '/home/<USER>/images/test/healthy_1.jpg'
img = image.load_img(image_path, target_size=(224, 224))
img = image.img_to_array(img)
img = np.expand_dims(img, axis=0)  # shape jadi (1, 224, 224, 3)

# 6. Prediksi dan ambil feature map dari masing-masing layer
featureOutputConvLayer = featureMapConvLayerModel.predict(img)
featureOutputMaxPoolingLayer = featureMapMaxPoolingLayerModel.predict(img)
featureOutputDropoutLayer = featureMapDropoutLayerModel.predict(img)

# 7. Contoh tampilkan bentuk output dari conv layer pertama
print("\nConv Layer 1 Output Shape:", featureOutputConvLayer[0].shape)
print("Max Pooling Layer 1 Output Shape:", featureOutputMaxPoolingLayer[0].shape)
print("Dropout Layer 1 Output Shape:", featureOutputDropoutLayer[0].shape)


for layer in model.layers:
    if 'conv' not in layer.name:
        continue
    filters , bias = layer.get_weights()
    print(layer.name , filters.shape)

# retrieve weights from the second hidden layer
filters , bias = model.layers[0].get_weights()

# normalize filter values to 0-1 so we can visualize them
f_min, f_max = filters.min(), filters.max()
filters = (filters - f_min) / (f_max - f_min)

n_filters =6
ix=1
fig = plt.figure(figsize=(20,15))
for i in range(n_filters):
    # get the filters
    f = filters[:,:,:,i]
    for j in range(3):
        # subplot for 6 filters and 3 channels
        plt.subplot(n_filters,3,ix)
        plt.imshow(f[:,:,j] ,cmap='gray')
        ix+=1
#plot the filters
plt.show()

def showCnnLayer(layer_names, feature_maps, n_features=16):
    """
    Visualize feature maps from CNN layers

    Args:
        layer_names: List of layer names
        feature_maps: List of feature map arrays
        n_features: Number of features to display (max 64)
    """
    for layer_name, feature_map in zip(layer_names, feature_maps):
        # Ensure we don't try to display more features than exist
        n_features = min(n_features, feature_map.shape[-1])

        plt.figure(figsize=(20, 5))
        plt.suptitle(f"Layer: {layer_name} - {feature_map.shape[-1]} channels")

        # Display first n_features channels
        for i in range(n_features):
            plt.subplot(2, 8, i+1)
            plt.imshow(feature_map[0, :, :, i], cmap='viridis')
            plt.axis('off')
            plt.title(f"Ch. {i}")

        plt.show()

# Get layer names
conv_layer_names = [model.layers[i].name for i in convLayerIdx]
pool_layer_names = [model.layers[i].name for i in maxPoolingLayerIdx]
dropout_layer_names = [model.layers[i].name for i in dropoutLayerIdx]

# Visualize feature maps (showing first 16 channels)
np.set_printoptions(threshold=np.inf)  # Show full array without truncation

print("\nVisualizing Conv Layers:")
showCnnLayer(conv_layer_names, featureOutputConvLayer, n_features=16)

print("\nVisualizing MaxPooling Layers:")
showCnnLayer(pool_layer_names, featureOutputMaxPoolingLayer, n_features=16)

datasetDir = '/path/to/your/dataset/'
model_dir = os.path.join(datasetDir, 'model')

if not os.path.exists(model_dir):
    os.makedirs(model_dir)  # This creates parent directories if needed
    print(f"Directory created: {model_dir}")
else:
    print(f"Directory already exists: {model_dir}")

model.save('/home/<USER>/model/model.h5')

checkpoint = tf.keras.callbacks.ModelCheckpoint(r'/home/<USER>/model/model.h5',
                          monitor='val_loss',
                          mode='min',
                          save_best_only=True,
                          verbose=1)
earlystop= tf.keras.callbacks.EarlyStopping(monitor='val_loss',
                       min_delta=0,
                       patience=10,
                       verbose=1,
                       restore_best_weights=True)

class MyCallback(tf.keras.callbacks.Callback):
  def on_epoch_end(self, epoch, logs={}):
    if (logs.get('accuracy') > 0.90):
      keys = list(logs.keys())
      print("Start epoch {} of training; got log keys: {}".format(epoch, keys))
      self.model.stop_training = True

callback = MyCallback()

callbacks=[checkpoint, earlystop, callback]

steps_per_epoch = max(1, train_datagen.samples // 8)
validation_steps = max(1, val_datagen.samples // 8)

history = model.fit(
    train_datagen,
    validation_data=val_datagen,
    epochs=30,
    steps_per_epoch=steps_per_epoch,
    validation_steps=validation_steps,
    callbacks=callbacks
)

acc = history.history['accuracy']
val_acc = history.history['val_accuracy']
loss = history.history['loss']
val_loss = history.history['val_loss']

epochs = range(len(acc))

plt.plot(epochs, acc)
plt.plot(epochs, val_acc)
plt.title('Training and Validation Accuracy')
plt.ylabel('accuracy')
plt.xlabel('epoch')
plt.legend(['train', 'val'], loc='upper left')
plt.figure()


plt.plot(epochs, loss)
plt.plot(epochs, val_loss)
plt.ylabel('loss')
plt.xlabel('epoch')
plt.legend(['train', 'val'], loc='upper left')
plt.title('Training and Validaion Loss')
plt.figure()


accuracy_df= pd.DataFrame.from_dict(history.history)
accuracy_df[["accuracy", "val_accuracy"]]

loss_df= pd.DataFrame.from_dict(history.history)
loss_df[["loss", "val_loss"]]

import os

if not os.path.exists("/home/<USER>"):
    os.mkdir("/home/<USER>")  # pakai os.mkdir, bukan shutil.os.mkdir


accuracy_df[["accuracy", "val_accuracy"]].to_csv("/home/<USER>/accuracy.csv")
loss_df[["loss", "val_loss"]].to_csv("/home/<USER>/loss.csv")

test_image=r'/home/<USER>/images/test/healthy_1.jpg' # healthly
image_result=Image.open(test_image)

test_image=image.load_img(test_image,target_size=(224,224))
test_image=image.img_to_array(test_image)
test_image=test_image/255
test_image=np.expand_dims(test_image,axis=0)
result=model.predict(test_image)
print(np.max(result) * 100)
Categories=['healthy', 'kutu daun', 'layu fusarium', 'ulat daun', 'unkown']
image_result=plt.imshow(image_result)
plt.title(Categories[np.argmax(result)])
plt.show()

model = tf.keras.models.load_model('/home/<USER>/model/model.h5')
converter = tf.lite.TFLiteConverter.from_keras_model(model)
tflite_model = converter.convert()
open("/home/<USER>/model/model.tflite", "wb").write(tflite_model)

import os
import matplotlib.pyplot as plt
from tensorflow.keras.preprocessing import image
import numpy as np
import tensorflow as tf
import natsort

# Label dan kategori
Categories = [0, 1, 2, 3, 4]
label_names = {
    0: "healthy",
    1: "kutu daun",
    2: "layu fusarium",
    3: "ulat daun",
    4: "unknown disease"
}

# Load model
model_predict = tf.keras.models.load_model('/home/<USER>/model/model.h5')
model_predict.compile(optimizer=tf.keras.optimizers.Adam(),
                      loss='categorical_crossentropy',
                      metrics=['accuracy'])

# Ambil maksimal 50 gambar dari folder test
uploaded = natsort.natsorted(os.listdir('/home/<USER>/images/test'))[:50]

image_name = []
image_conf = []
predict_result = []
evaluate_pred = []
evaluate_test = []

# Proses gambar
for fn in uploaded:
    path = '/home/<USER>/images/test/' + fn

    img = image.load_img(path, color_mode="rgb", target_size=(224, 224))
    img_arr = image.img_to_array(img) / 255.0
    img_arr = np.expand_dims(img_arr, axis=0)

    prediction = model_predict.predict(img_arr)

    pred_index = np.argmax(prediction)
    pred_label = label_names[pred_index]
    predict_result.append(pred_label)
    evaluate_pred.append(pred_index)
    image_conf.append(np.max(prediction))
    image_name.append(path)

    # Ambil label asli dari nama file
    true_label_name = fn.split("_")[0].replace("-", " ")
    if true_label_name == "healthy":
        evaluate_test.append(0)
    elif true_label_name == "kutu daun":
        evaluate_test.append(1)
    elif true_label_name == "layu fusarium":
        evaluate_test.append(2)
    elif true_label_name == "ulat daun":
        evaluate_test.append(3)
    else:
        evaluate_test.append(4)

# ✅ Tampilkan semua gambar dengan label besar
plt.figure(figsize=(14, 4 * (len(image_name) // 2)))  # lebar x tinggi
for n in range(len(image_name)):
    plt.subplot((len(image_name) // 2) + 1, 2, n + 1)
    plt.subplots_adjust(hspace=0.4, wspace=0.2)  # spacing antar gambar

    img = image.load_img(image_name[n], color_mode="rgb", target_size=(224, 224))
    plt.imshow(img)

    true_label = label_names[evaluate_test[n]]
    pred_label = predict_result[n]
    conf = round(image_conf[n] * 100, 2)

    title = f"Actual: {true_label}\nPredict: {pred_label} ({conf}%)"
    plt.title(title, fontsize=10)  # 🔍 ukuran font diperbesar
    plt.axis('off')

plt.show()

# ✅ Simpan versi resolusi tinggi
plt.figure(figsize=(14, 4 * (len(image_name) // 2)), dpi=300)
for n in range(len(image_name)):
    plt.subplot((len(image_name) // 2) + 1, 2, n + 1)
    plt.subplots_adjust(hspace=0.4, wspace=0.2)

    img = image.load_img(image_name[n], color_mode="rgb", target_size=(224, 224))
    plt.imshow(img)

    true_label = label_names[evaluate_test[n]]
    pred_label = predict_result[n]
    conf = round(image_conf[n] * 100, 2)

    title = f"Actual: {true_label}\nPredict: {pred_label} ({conf}%)"
    plt.title(title, fontsize=10)  # 🔍 simpan dengan font lebih besar
    plt.axis('off')

os.makedirs("/home/<USER>", exist_ok=True)
plt.savefig('/home/<USER>/50_predictions_2cols_bigfont.png', bbox_inches='tight', dpi=300)
plt.close()


len(predict_result)
# len(test_result_val)

tmp_test_result = {"Actual": evaluate_test, "Prediction": evaluate_pred}
test_result = pd.DataFrame(data=tmp_test_result)
test_result.to_csv("/home/<USER>/test_result.csv", index=False)

test_result

test_result.to_csv("/home/<USER>/test_result")

evaluate_test = []
for i in natsort.natsorted(os.listdir('/home/<USER>/images/test')):
  disease = i.split("_")[0]
  if disease == "healthy":
    diseaseInt = 0
  elif disease == "kutu-daun":
    diseaseInt = 1
  elif disease == "layu-fusarium":
    diseaseInt = 2
  elif disease == "ulat-daun":
    diseaseInt = 3
  elif disease == "unknown":
    diseaseInt = 4
  evaluate_test.append(diseaseInt)

# Akurasi
acc = accuracy_score(evaluate_test, evaluate_pred)
print(f'Accuracy Score : {acc:.4f}\n')

# Confusion Matrix
cf_matrix = confusion_matrix(evaluate_test, evaluate_pred)
print(f'Confusion Matrix:\n{cf_matrix}\n')

# Classification Report (formatted to 4 decimal places)
cls_report = classification_report(evaluate_test, evaluate_pred, digits=4)
print(f'Classification Report:\n{cls_report}')


plt.figure(figsize=(10,9))
sns.heatmap(cf_matrix, annot=True, cmap="YlGnBu")
sns.set(font_scale=2)
plt.title('Confusion Matrix \n', fontsize=20)
plt.ylabel('Actal Values \n', fontsize=14)
plt.xlabel('\nPredicted Values', fontsize=14)
plt.show()

from sklearn.preprocessing import label_binarize
from sklearn.metrics import roc_curve, auc
from sklearn.metrics import RocCurveDisplay
import matplotlib.pyplot as plt
import numpy as np


from sklearn.metrics import roc_curve, auc
from sklearn.preprocessing import label_binarize
import matplotlib.pyplot as plt
import numpy as np

# Misal y_test = [0, 1, 2, 1, 0, ...] dan y_score = model.predict_proba(X_test)
# One-hot encode y_test
y_test_binarized = label_binarize(evaluate_test, classes=[0, 1, 2, 3, 4])
n_classes = y_test_binarized.shape[1]

# Load and preprocess the test images to get y_score
test_images = []
for fn in natsort.natsorted(os.listdir('/home/<USER>/images/test')):
    path = '/home/<USER>/images/test/' + fn
    img = image.load_img(path, color_mode="rgb", target_size=(224, 224))
    img_arr = image.img_to_array(img) / 255.0
    test_images.append(img_arr)

X_test = np.array(test_images)
y_score = model_predict.predict(X_test)


# Compute ROC curve and ROC area for each class
fpr = {}
tpr = {}
roc_auc = {}

for i in range(n_classes):
    fpr[i], tpr[i], _ = roc_curve(y_test_binarized[:, i], y_score[:, i])
    roc_auc[i] = auc(fpr[i], tpr[i])

# Compute micro-average ROC curve and ROC area
fpr["micro"], tpr["micro"], _ = roc_curve(y_test_binarized.ravel(), y_score.ravel())
roc_auc["micro"] = auc(fpr["micro"], tpr["micro"])

# Plot all ROC curves
plt.figure(figsize=(10, 8)) # Increase figure size
for i in range(n_classes):
    plt.plot(fpr[i], tpr[i],
             label='ROC curve of class {0} (area = {1:0.4f})'
             ''.format(i, roc_auc[i]))
plt.plot([0, 1], [0, 1], 'k--', lw=2)
plt.xlabel('False Positive Rate', fontsize=12) # Increase font size
plt.ylabel('True Positive Rate', fontsize=12) # Increase font size
plt.title('Multi-class ROC-AUC Curve', fontsize=14) # Increase font size
plt.legend(loc="lower right", fontsize=10) # Increase font size of legend
plt.grid(True) # Add grid
plt.show()