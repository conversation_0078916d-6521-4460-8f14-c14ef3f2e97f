"""
KODE SIAP COPY-PASTE KE NOTEBOOK BIRD CLASSIFICATION
Setiap section adalah satu cell notebook yang terpisah
"""

# ============================================================================
# CELL 1: IMPORT LIBRARIES TAMBAHAN
# Tambahkan ke cell import yang sudah ada di notebook Anda
# ============================================================================

# Import tambahan untuk evaluasi lengkap
import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_curve, auc
)
from sklearn.preprocessing import label_binarize
from sklearn.model_selection import StratifiedKFold
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Set style untuk visualisasi
plt.style.use('default')
sns.set_palette("husl")

print("✅ Import libraries tambahan berhasil!")

# ============================================================================
# CELL 2: KONFIGURASI PATH DAN PARAMETER
# Sesuaikan dengan setup Anda
# ============================================================================

# KONFIGURASI - SESUAIKAN DENGAN SETUP ANDA
TRAIN_DIR = '/content/dataset/train'  # Ganti dengan path training Anda
TEST_DIR = '/content/dataset/test'    # Ganti dengan path testing Anda
MODEL_PATH = './models/best_bird_cnn_model.h5'  # Ganti dengan path model Anda

CLASS_NAMES = [
    'Lonchura leucogastroides',
    'Lonchura maja', 
    'Lonchura punctulata',
    'Passer montanus',
    'Unknown'
]  # Sesuaikan dengan nama kelas Anda

TARGET_SIZE = (224, 224)
BATCH_SIZE = 16

print("✅ Konfigurasi berhasil!")
print(f"Training directory: {TRAIN_DIR}")
print(f"Testing directory: {TEST_DIR}")
print(f"Model path: {MODEL_PATH}")
print(f"Classes: {CLASS_NAMES}")

# ============================================================================
# CELL 3: EXPLORATORY DATA ANALYSIS (EDA) - WAJIB UNTUK SKRIPSI
# ============================================================================

print("=== 📊 EXPLORATORY DATA ANALYSIS (EDA) ===")

# Analisis distribusi dataset
def analyze_dataset_distribution():
    """Analisis distribusi dataset untuk skripsi"""
    
    print("📊 Menganalisis distribusi dataset...")
    
    # Training data distribution
    train_counts = {}
    for class_name in CLASS_NAMES:
        class_path = os.path.join(TRAIN_DIR, class_name)
        if os.path.exists(class_path):
            train_counts[class_name] = len([f for f in os.listdir(class_path) 
                                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
        else:
            train_counts[class_name] = 0
    
    # Testing data distribution  
    test_counts = {}
    for class_name in CLASS_NAMES:
        class_path = os.path.join(TEST_DIR, class_name)
        if os.path.exists(class_path):
            test_counts[class_name] = len([f for f in os.listdir(class_path) 
                                         if f.lower().endswith(('.png', '.jpg', '.jpeg'))])
        else:
            test_counts[class_name] = 0
    
    # Print statistics
    total_train = sum(train_counts.values())
    total_test = sum(test_counts.values())
    total_all = total_train + total_test
    
    print(f"\n📈 STATISTIK DATASET:")
    print(f"Total Training Images: {total_train}")
    print(f"Total Testing Images: {total_test}")
    print(f"Total Dataset: {total_all}")
    
    print(f"\n📋 DISTRIBUSI PER KELAS:")
    for class_name in CLASS_NAMES:
        train_count = train_counts.get(class_name, 0)
        test_count = test_counts.get(class_name, 0)
        total_count = train_count + test_count
        percentage = (total_count / total_all) * 100 if total_all > 0 else 0
        print(f"{class_name}:")
        print(f"  Training: {train_count} | Testing: {test_count} | Total: {total_count} ({percentage:.1f}%)")
    
    # Visualisasi distribusi
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Training distribution
    bars1 = ax1.bar(range(len(train_counts)), list(train_counts.values()), 
                    color='skyblue', alpha=0.8, edgecolor='navy')
    ax1.set_title('Distribusi Data Training per Kelas', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Kelas Burung')
    ax1.set_ylabel('Jumlah Gambar')
    ax1.set_xticks(range(len(train_counts)))
    ax1.set_xticklabels(list(train_counts.keys()), rotation=45, ha='right')
    ax1.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, count in zip(bars1, train_counts.values()):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + max(train_counts.values()) * 0.01,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # Testing distribution
    bars2 = ax2.bar(range(len(test_counts)), list(test_counts.values()), 
                    color='lightcoral', alpha=0.8, edgecolor='darkred')
    ax2.set_title('Distribusi Data Testing per Kelas', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Kelas Burung')
    ax2.set_ylabel('Jumlah Gambar')
    ax2.set_xticks(range(len(test_counts)))
    ax2.set_xticklabels(list(test_counts.keys()), rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, count in zip(bars2, test_counts.values()):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + max(test_counts.values()) * 0.01,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # Combined distribution
    combined_counts = [train_counts.get(name, 0) + test_counts.get(name, 0) for name in CLASS_NAMES]
    bars3 = ax3.bar(range(len(CLASS_NAMES)), combined_counts, 
                    color='lightgreen', alpha=0.8, edgecolor='darkgreen')
    ax3.set_title('Total Distribusi Dataset', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Kelas Burung')
    ax3.set_ylabel('Total Gambar')
    ax3.set_xticks(range(len(CLASS_NAMES)))
    ax3.set_xticklabels(CLASS_NAMES, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    
    # Add value labels
    for bar, count in zip(bars3, combined_counts):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + max(combined_counts) * 0.01,
                str(count), ha='center', va='bottom', fontweight='bold')
    
    # Class balance pie chart
    percentages = [(count/total_all)*100 for count in combined_counts]
    colors_pie = plt.cm.Set3(np.linspace(0, 1, len(CLASS_NAMES)))
    
    wedges, texts, autotexts = ax4.pie(percentages, labels=CLASS_NAMES, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90)
    ax4.set_title('Proporsi Kelas dalam Dataset', fontsize=14, fontweight='bold')
    
    # Make percentage text bold
    for autotext in autotexts:
        autotext.set_color('white')
        autotext.set_fontweight('bold')
    
    plt.tight_layout()
    plt.savefig('dataset_eda_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return train_counts, test_counts

# Jalankan EDA
train_counts, test_counts = analyze_dataset_distribution()

# ============================================================================
# CELL 4: SAMPLE IMAGES VISUALIZATION - WAJIB UNTUK SKRIPSI
# ============================================================================

def show_sample_images_per_class(samples_per_class=3):
    """Tampilkan sample gambar dari setiap kelas"""
    
    print("🖼️ Menampilkan sample gambar per kelas...")
    
    n_classes = len(CLASS_NAMES)
    fig, axes = plt.subplots(n_classes, samples_per_class, figsize=(15, 3 * n_classes))
    
    if n_classes == 1:
        axes = axes.reshape(1, -1)
    elif samples_per_class == 1:
        axes = axes.reshape(-1, 1)
    
    for class_idx, class_name in enumerate(CLASS_NAMES):
        class_path = os.path.join(TRAIN_DIR, class_name)
        
        if os.path.exists(class_path):
            image_files = [f for f in os.listdir(class_path) 
                          if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
            
            for sample_idx in range(min(samples_per_class, len(image_files))):
                if samples_per_class == 1:
                    ax = axes[class_idx]
                else:
                    ax = axes[class_idx, sample_idx]
                
                try:
                    img_path = os.path.join(class_path, image_files[sample_idx])
                    img = plt.imread(img_path)
                    ax.imshow(img)
                    
                    if sample_idx == 0:  # Only show class name on first image
                        ax.set_title(f'{class_name}', fontsize=10, fontweight='bold')
                    else:
                        ax.set_title(f'Sample {sample_idx + 1}', fontsize=8)
                    
                    ax.axis('off')
                    
                except Exception as e:
                    ax.text(0.5, 0.5, f'Error\nloading\nimage', ha='center', va='center',
                           transform=ax.transAxes, fontsize=8)
                    ax.axis('off')
            
            # Hide unused subplots for this class
            for sample_idx in range(len(image_files), samples_per_class):
                if samples_per_class > 1:
                    axes[class_idx, sample_idx].axis('off')
    
    plt.suptitle('Sample Gambar per Kelas Burung', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('sample_images_per_class.png', dpi=300, bbox_inches='tight')
    plt.show()

# Jalankan sample images visualization
show_sample_images_per_class(samples_per_class=3)

# ============================================================================
# CELL 5: ENHANCED TRAINING HISTORY - GANTI YANG SUDAH ADA
# ============================================================================

# Ganti cell training history yang sudah ada dengan kode ini:
def plot_enhanced_training_history(history):
    """Plot training history yang lebih komprehensif untuk skripsi"""

    print("📊 Creating enhanced training history visualization...")

    # Extract data
    acc = history.history['accuracy']
    val_acc = history.history['val_accuracy']
    loss = history.history['loss']
    val_loss = history.history['val_loss']
    epochs_range = range(1, len(acc) + 1)

    # Create comprehensive plots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Accuracy plot
    ax1.plot(epochs_range, acc, 'b-', linewidth=2, label='Training Accuracy', marker='o', markersize=4)
    ax1.plot(epochs_range, val_acc, 'r-', linewidth=2, label='Validation Accuracy', marker='s', markersize=4)
    ax1.set_title('🎯 Model Accuracy', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Accuracy')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim([0, 1])

    # Loss plot
    ax2.plot(epochs_range, loss, 'b-', linewidth=2, label='Training Loss', marker='o', markersize=4)
    ax2.plot(epochs_range, val_loss, 'r-', linewidth=2, label='Validation Loss', marker='s', markersize=4)
    ax2.set_title('📉 Model Loss', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Loss')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # Overfitting analysis
    acc_diff = np.array(acc) - np.array(val_acc)
    ax3.plot(epochs_range, acc_diff, 'g-', linewidth=2, label='Training - Validation', marker='d', markersize=4)
    ax3.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    ax3.set_title('📈 Overfitting Analysis', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Epoch')
    ax3.set_ylabel('Accuracy Difference')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # Training summary
    final_train_acc = acc[-1]
    final_val_acc = val_acc[-1]
    best_val_acc = max(val_acc)
    best_epoch = val_acc.index(best_val_acc) + 1
    overfitting_gap = final_train_acc - final_val_acc

    summary_text = f"""TRAINING SUMMARY:

Final Training Accuracy: {final_train_acc:.4f}
Final Validation Accuracy: {final_val_acc:.4f}
Best Validation Accuracy: {best_val_acc:.4f}
Best Epoch: {best_epoch}
Overfitting Gap: {overfitting_gap:.4f}

INTERPRETATION:
{'✅ Good generalization' if overfitting_gap < 0.1 else '⚠️ Potential overfitting'}
{'✅ Stable training' if abs(val_acc[-1] - val_acc[-5]) < 0.02 else '⚠️ Unstable convergence'}"""

    ax4.text(0.1, 0.5, summary_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='center',
             bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
    ax4.set_title('📋 Training Summary', fontsize=14, fontweight='bold')
    ax4.axis('off')

    plt.tight_layout()
    plt.savefig('enhanced_training_history.png', dpi=300, bbox_inches='tight')
    plt.show()

    # Print detailed summary
    print(f"\n📊 TRAINING RESULTS SUMMARY:")
    print(f"Training completed in {len(epochs_range)} epochs")
    print(f"Best validation accuracy: {best_val_acc:.4f} at epoch {best_epoch}")
    print(f"Final training accuracy: {final_train_acc:.4f}")
    print(f"Final validation accuracy: {final_val_acc:.4f}")
    print(f"Overfitting gap: {overfitting_gap:.4f}")

    return {
        'final_train_acc': final_train_acc,
        'final_val_acc': final_val_acc,
        'best_val_acc': best_val_acc,
        'best_epoch': best_epoch,
        'overfitting_gap': overfitting_gap
    }

# Jalankan enhanced training history (gunakan variabel history dari training Anda)
training_summary = plot_enhanced_training_history(history)

# ============================================================================
# CELL 6: SETUP TEST GENERATOR - PERSIAPAN EVALUASI
# ============================================================================

# Setup test generator untuk evaluasi
print("🔧 Setting up test data generator...")

test_datagen = ImageDataGenerator(rescale=1./255)
test_generator = test_datagen.flow_from_directory(
    TEST_DIR,
    target_size=TARGET_SIZE,
    batch_size=BATCH_SIZE,
    class_mode='categorical',
    shuffle=False  # Important: jangan shuffle untuk evaluasi
)

print(f"✅ Test generator setup complete!")
print(f"Found {test_generator.samples} test images")
print(f"Classes found: {list(test_generator.class_indices.keys())}")

# ============================================================================
# CELL 7: COMPREHENSIVE MODEL EVALUATION - WAJIB UNTUK SKRIPSI
# ============================================================================

def comprehensive_model_evaluation():
    """Evaluasi model yang komprehensif untuk skripsi"""

    print("=== 🎯 COMPREHENSIVE MODEL EVALUATION ===")

    # Load best model
    print("📥 Loading best trained model...")
    best_model = tf.keras.models.load_model(MODEL_PATH)

    # Get predictions
    print("🔮 Making predictions on test set...")
    test_generator.reset()  # Reset generator
    predictions = best_model.predict(test_generator, verbose=1)
    predicted_classes = np.argmax(predictions, axis=1)
    true_classes = test_generator.classes

    # Calculate comprehensive metrics
    accuracy = accuracy_score(true_classes, predicted_classes)
    precision_macro = precision_score(true_classes, predicted_classes, average='macro', zero_division=0)
    recall_macro = recall_score(true_classes, predicted_classes, average='macro', zero_division=0)
    f1_macro = f1_score(true_classes, predicted_classes, average='macro', zero_division=0)

    precision_weighted = precision_score(true_classes, predicted_classes, average='weighted', zero_division=0)
    recall_weighted = recall_score(true_classes, predicted_classes, average='weighted', zero_division=0)
    f1_weighted = f1_score(true_classes, predicted_classes, average='weighted', zero_division=0)

    # Print overall metrics
    print(f"\n📊 OVERALL PERFORMANCE METRICS:")
    print(f"Overall Accuracy: {accuracy:.4f}")
    print(f"Macro-average Precision: {precision_macro:.4f}")
    print(f"Macro-average Recall: {recall_macro:.4f}")
    print(f"Macro-average F1-score: {f1_macro:.4f}")
    print(f"Weighted-average Precision: {precision_weighted:.4f}")
    print(f"Weighted-average Recall: {recall_weighted:.4f}")
    print(f"Weighted-average F1-score: {f1_weighted:.4f}")

    # Detailed classification report
    print(f"\n📋 DETAILED CLASSIFICATION REPORT:")
    cls_report = classification_report(true_classes, predicted_classes,
                                     target_names=CLASS_NAMES, digits=4)
    print(cls_report)

    # Per-class accuracy analysis
    print(f"\n🎯 PER-CLASS ACCURACY ANALYSIS:")
    cm = confusion_matrix(true_classes, predicted_classes)
    class_accuracies = cm.diagonal() / cm.sum(axis=1)

    for i, (class_name, acc) in enumerate(zip(CLASS_NAMES, class_accuracies)):
        total_samples = cm.sum(axis=1)[i]
        correct_predictions = cm.diagonal()[i]
        print(f"{class_name}: {acc:.4f} ({correct_predictions}/{total_samples})")

    # Model efficiency
    model_size_mb = best_model.count_params() * 4 / (1024 * 1024)
    print(f"\n⚙️ MODEL EFFICIENCY:")
    print(f"Total Parameters: {best_model.count_params():,}")
    print(f"Model Size: {model_size_mb:.2f} MB")

    return {
        'model': best_model,
        'predictions': predictions,
        'predicted_classes': predicted_classes,
        'true_classes': true_classes,
        'accuracy': accuracy,
        'precision_macro': precision_macro,
        'recall_macro': recall_macro,
        'f1_macro': f1_macro,
        'precision_weighted': precision_weighted,
        'recall_weighted': recall_weighted,
        'f1_weighted': f1_weighted,
        'class_accuracies': class_accuracies,
        'confusion_matrix': cm,
        'classification_report': cls_report,
        'model_size_mb': model_size_mb
    }

# Jalankan comprehensive evaluation
evaluation_results = comprehensive_model_evaluation()
