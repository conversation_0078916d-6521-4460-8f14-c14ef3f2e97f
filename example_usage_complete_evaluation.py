"""
<PERSON><PERSON><PERSON>an <PERSON>luasi <PERSON>gkap untuk Bird Classification CNN
Script ini menunjukkan cara menggunakan semua komponen evaluasi yang diperlukan untuk skripsi
"""

import os
import sys
import numpy as np
import tensorflow as tf
from tensorflow.keras.preprocessing.image import ImageDataGenerator

# Import evaluator yang sudah dibuat
from bird_classification_complete_evaluation import run_complete_evaluation

def setup_data_generators(train_dir, test_dir, target_size=(224, 224), batch_size=16):
    """Setup data generators untuk evaluasi"""
    
    # Test data generator (no augmentation)
    test_datagen = ImageDataGenerator(rescale=1./255)
    
    test_generator = test_datagen.flow_from_directory(
        test_dir,
        target_size=target_size,
        batch_size=batch_size,
        class_mode='categorical',
        shuffle=False  # Important for evaluation
    )
    
    return test_generator

def main():
    """Fungsi utama untuk menjalankan evaluasi lengkap"""
    
    # ========================================
    # KONFIGURASI PATH DAN PARAMETER
    # ========================================
    
    # Path ke model yang sudah ditraining
    MODEL_PATH = 'best_bird_model.h5'  # Sesuaikan dengan path model Anda
    
    # Path ke direktori dataset
    TRAIN_DIR = 'dataset/train'  # Sesuaikan dengan struktur folder Anda
    TEST_DIR = 'dataset/test'    # Sesuaikan dengan struktur folder Anda
    
    # Path ke file training history (opsional)
    HISTORY_CSV_PATH = 'training_history.csv'  # Jika Anda menyimpan history ke CSV
    
    # Nama kelas burung (sesuaikan dengan kelas Anda)
    CLASS_NAMES = [
        'Lonchura leucogastroides',
        'Lonchura maja', 
        'Lonchura punctulata',
        'Passer montanus',
        'Unknown'
    ]
    
    # Parameter model
    TARGET_SIZE = (224, 224)
    BATCH_SIZE = 16
    
    # ========================================
    # VALIDASI FILE DAN DIREKTORI
    # ========================================
    
    print("🔍 VALIDASI FILE DAN DIREKTORI...")
    
    # Cek apakah model ada
    if not os.path.exists(MODEL_PATH):
        print(f"❌ Error: Model tidak ditemukan di {MODEL_PATH}")
        print("   Pastikan Anda sudah melatih model dan menyimpannya.")
        return
    
    # Cek apakah direktori dataset ada
    if not os.path.exists(TRAIN_DIR):
        print(f"❌ Error: Direktori training tidak ditemukan di {TRAIN_DIR}")
        return
        
    if not os.path.exists(TEST_DIR):
        print(f"❌ Error: Direktori testing tidak ditemukan di {TEST_DIR}")
        return
    
    # Cek struktur direktori
    train_classes = [d for d in os.listdir(TRAIN_DIR) if os.path.isdir(os.path.join(TRAIN_DIR, d))]
    test_classes = [d for d in os.listdir(TEST_DIR) if os.path.isdir(os.path.join(TEST_DIR, d))]
    
    print(f"✅ Model ditemukan: {MODEL_PATH}")
    print(f"✅ Training classes: {train_classes}")
    print(f"✅ Testing classes: {test_classes}")
    
    # ========================================
    # SETUP DATA GENERATOR
    # ========================================
    
    print("\n📊 SETUP DATA GENERATOR...")
    
    try:
        test_generator = setup_data_generators(TRAIN_DIR, TEST_DIR, TARGET_SIZE, BATCH_SIZE)
        print(f"✅ Test generator berhasil dibuat")
        print(f"   - Total test samples: {test_generator.samples}")
        print(f"   - Batch size: {test_generator.batch_size}")
        print(f"   - Classes: {list(test_generator.class_indices.keys())}")
        
    except Exception as e:
        print(f"❌ Error dalam setup data generator: {str(e)}")
        return
    
    # ========================================
    # LOAD DAN VALIDASI MODEL
    # ========================================
    
    print("\n🤖 LOAD DAN VALIDASI MODEL...")
    
    try:
        model = tf.keras.models.load_model(MODEL_PATH)
        print(f"✅ Model berhasil dimuat")
        print(f"   - Total parameters: {model.count_params():,}")
        print(f"   - Model size: {model.count_params() * 4 / (1024*1024):.2f} MB")
        print(f"   - Input shape: {model.input_shape}")
        print(f"   - Output shape: {model.output_shape}")
        
    except Exception as e:
        print(f"❌ Error dalam load model: {str(e)}")
        return
    
    # ========================================
    # JALANKAN EVALUASI LENGKAP
    # ========================================
    
    print("\n🚀 MEMULAI EVALUASI LENGKAP...")
    print("=" * 60)
    
    try:
        # Jalankan evaluasi lengkap
        results = run_complete_evaluation(
            model_path=MODEL_PATH,
            train_dir=TRAIN_DIR,
            test_dir=TEST_DIR,
            class_names=CLASS_NAMES,
            test_generator=test_generator,
            history_csv_path=HISTORY_CSV_PATH if os.path.exists(HISTORY_CSV_PATH) else None
        )
        
        # ========================================
        # RINGKASAN HASIL EVALUASI
        # ========================================
        
        print("\n📋 RINGKASAN HASIL EVALUASI:")
        print("=" * 50)
        
        eval_results = results['evaluation_results']
        roc_results = results['roc_results']
        
        print(f"Overall Accuracy: {eval_results['accuracy']:.4f}")
        print(f"Macro-avg Precision: {eval_results['precision_macro']:.4f}")
        print(f"Macro-avg Recall: {eval_results['recall_macro']:.4f}")
        print(f"Macro-avg F1-Score: {eval_results['f1_macro']:.4f}")
        print(f"Macro-avg AUC: {roc_results['macro']:.4f}")
        
        print("\nPer-Class Accuracy:")
        for i, (class_name, acc) in enumerate(zip(CLASS_NAMES, eval_results['class_accuracies'])):
            print(f"  {class_name}: {acc:.4f}")
        
        print("\nPer-Class AUC:")
        for i, class_name in enumerate(CLASS_NAMES):
            print(f"  {class_name}: {roc_results[i]:.4f}")
        
        # ========================================
        # INTERPRETASI HASIL
        # ========================================
        
        print("\n🎯 INTERPRETASI HASIL:")
        print("=" * 30)
        
        accuracy = eval_results['accuracy']
        macro_auc = roc_results['macro']
        
        # Interpretasi accuracy
        if accuracy >= 0.9:
            acc_interpretation = "Excellent (>90%)"
        elif accuracy >= 0.8:
            acc_interpretation = "Good (80-90%)"
        elif accuracy >= 0.7:
            acc_interpretation = "Fair (70-80%)"
        else:
            acc_interpretation = "Needs Improvement (<70%)"
        
        # Interpretasi AUC
        if macro_auc >= 0.9:
            auc_interpretation = "Excellent (>0.9)"
        elif macro_auc >= 0.8:
            auc_interpretation = "Good (0.8-0.9)"
        elif macro_auc >= 0.7:
            auc_interpretation = "Fair (0.7-0.8)"
        else:
            auc_interpretation = "Poor (<0.7)"
        
        print(f"Model Accuracy: {acc_interpretation}")
        print(f"Model AUC Performance: {auc_interpretation}")
        
        # Analisis class imbalance
        class_accs = eval_results['class_accuracies']
        acc_std = np.std(class_accs)
        
        if acc_std > 0.1:
            print(f"⚠️  High variance in class performance (std: {acc_std:.3f})")
            print("   Consider addressing class imbalance or improving data quality")
        else:
            print(f"✅ Consistent performance across classes (std: {acc_std:.3f})")
        
        # ========================================
        # REKOMENDASI UNTUK SKRIPSI
        # ========================================
        
        print("\n📝 REKOMENDASI UNTUK SKRIPSI:")
        print("=" * 35)
        
        print("✅ Komponen yang sudah lengkap untuk skripsi:")
        print("   1. Exploratory Data Analysis (EDA)")
        print("   2. Training History Visualization")
        print("   3. Comprehensive Evaluation Metrics")
        print("   4. Confusion Matrix Analysis")
        print("   5. ROC Curve Analysis")
        print("   6. Performance Summary Report")
        
        print("\n📊 File visualisasi yang dihasilkan:")
        print("   - dataset_distribution.png")
        print("   - sample_images_per_class.png")
        print("   - training_history_comprehensive.png")
        print("   - confusion_matrix.png")
        print("   - roc_curves.png")
        print("   - performance_summary_comprehensive.png")
        
        print("\n💡 Tips untuk skripsi:")
        print("   1. Gunakan semua visualisasi dalam bab Results/Hasil")
        print("   2. Diskusikan interpretasi hasil di bab Discussion")
        print("   3. Bandingkan dengan penelitian serupa")
        print("   4. Jelaskan limitasi dan future work")
        
        print("\n🎉 EVALUASI BERHASIL DISELESAIKAN!")
        
    except Exception as e:
        print(f"❌ Error dalam evaluasi: {str(e)}")
        print("Stacktrace:")
        import traceback
        traceback.print_exc()
        return
    
    return results

def create_sample_training_history():
    """Buat contoh file training history jika tidak ada"""
    
    # Contoh data training history
    sample_history = {
        'epoch': list(range(1, 51)),
        'accuracy': [0.2 + 0.015*i + np.random.normal(0, 0.01) for i in range(50)],
        'val_accuracy': [0.18 + 0.012*i + np.random.normal(0, 0.015) for i in range(50)],
        'loss': [2.0 - 0.035*i + np.random.normal(0, 0.05) for i in range(50)],
        'val_loss': [2.1 - 0.032*i + np.random.normal(0, 0.08) for i in range(50)]
    }
    
    # Pastikan nilai dalam range yang masuk akal
    sample_history['accuracy'] = np.clip(sample_history['accuracy'], 0, 1)
    sample_history['val_accuracy'] = np.clip(sample_history['val_accuracy'], 0, 1)
    sample_history['loss'] = np.clip(sample_history['loss'], 0.1, 3)
    sample_history['val_loss'] = np.clip(sample_history['val_loss'], 0.1, 3)
    
    import pandas as pd
    df = pd.DataFrame(sample_history)
    df.to_csv('sample_training_history.csv', index=False)
    
    print("✅ Sample training history created: sample_training_history.csv")
    return 'sample_training_history.csv'

if __name__ == "__main__":
    print("🔥 BIRD CLASSIFICATION CNN - EVALUASI LENGKAP UNTUK SKRIPSI 🔥")
    print("=" * 70)
    
    # Jalankan evaluasi
    results = main()
    
    if results:
        print("\n✨ Semua komponen evaluasi untuk skripsi telah berhasil dijalankan!")
        print("   Anda sekarang memiliki semua visualisasi dan metrik yang diperlukan.")
    else:
        print("\n❌ Evaluasi gagal. Periksa konfigurasi dan coba lagi.")
        
        # Tawarkan untuk membuat sample data
        create_sample = input("\n🤔 Apakah Anda ingin membuat sample training history? (y/n): ")
        if create_sample.lower() == 'y':
            create_sample_training_history()
